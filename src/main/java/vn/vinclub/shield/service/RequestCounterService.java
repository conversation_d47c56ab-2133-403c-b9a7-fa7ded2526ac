package vn.vinclub.shield.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.IntegerCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.enums.Action;

import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class RequestCounterService {

    private final RedissonClient redissonClient;

    // Cache TTL configuration
    @Value("${shield.cache.velocity.ttl-minutes:60}")
    private int velocityTtlMinutes;

    @Value("${shield.timezone:GMT+7}")
    private String timezone;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHH");
    private static final DateTimeFormatter MINUTE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    /**
     * Get current time in configured timezone
     */
    private ZonedDateTime getCurrentTime() {
        return ZonedDateTime.now(ZoneId.of(timezone));
    }

    /**
     * Check and increment velocity counter for IP address
     * Returns true if this is a high-frequency pattern (multiple requests in short time)
     */
    @Profiler
    public boolean checkAndIncrementVelocityCounter(String ipAddress, Action action) {
        String key = buildVelocityKey(ipAddress, action);
        RBucket<Integer> counter = redissonClient.getBucket(key, new IntegerCodec());
        
        Integer currentCount = counter.get();
        if (currentCount == null) {
            // First request in this time window
            counter.set(1, Duration.ofMinutes(velocityTtlMinutes));
            return false;
        } else {
            // Increment counter
            counter.set(currentCount + 1, Duration.ofMinutes(velocityTtlMinutes));
            // High frequency if more than 10 requests in the time window
            return currentCount >= 10;
        }
    }

    /**
     * Check if IP has rapid-fire pattern (multiple requests per minute)
     */
    @Profiler
    public boolean checkRapidFirePattern(String ipAddress, Action action) {
        String minuteKey = buildMinuteKey(ipAddress, action);
        RSet<String> minuteSet = redissonClient.getSet(minuteKey);
        
        String requestId = System.currentTimeMillis() + "_" + Thread.currentThread().threadId();
        minuteSet.add(requestId);
        minuteSet.expire(Duration.ofMinutes(5)); // Keep for 5 minutes
        
        // Rapid fire if more than 5 requests in the same minute
        return minuteSet.size() > 5;
    }

    /**
     * Check if there are multiple new indicators detected in short time
     */
    @Profiler
    public boolean checkMultipleNewIndicators(String sourceIdentifier, Action action, String indicatorType) {
        String key = buildNewIndicatorKey(sourceIdentifier, action);
        RScoredSortedSet<String> indicators = redissonClient.getScoredSortedSet(key);
        
        long currentTime = System.currentTimeMillis();
        indicators.add(currentTime, indicatorType);
        indicators.expire(Duration.ofHours(1)); // Keep for 1 hour
        
        // Remove old entries (older than 1 hour)
        long oneHourAgo = currentTime - TimeUnit.HOURS.toMillis(1);
        indicators.removeRangeByScore(0, true, oneHourAgo, true);
        
        // Multiple new indicators if 3 or more different types in the last hour
        return indicators.size() >= 3;
    }

    /**
     * Check if activity is happening during off-hours
     */
    @Profiler
    public boolean isOffHoursActivity() {
        int hour = getCurrentTime().getHour();
        return hour < 5;
    }

    /**
     * Check for automated behavior pattern using time-window analysis
     */
    @Profiler
    public boolean checkAutomatedBehaviorPattern(String ipAddress, Action action) {
        String key = buildAutomatedPatternKey(ipAddress, action);
        RScoredSortedSet<String> requests = redissonClient.getScoredSortedSet(key);
        
        long currentTime = System.currentTimeMillis();
        String requestId = currentTime + "_" + Thread.currentThread().threadId();
        requests.add(currentTime, requestId);
        requests.expire(Duration.ofMinutes(30)); // Keep for 30 minutes
        
        // Remove old entries (older than 5 minutes)
        long fiveMinutesAgo = currentTime - TimeUnit.MINUTES.toMillis(5);
        requests.removeRangeByScore(0, true, fiveMinutesAgo, true);
        
        // Automated behavior if more than 15 requests in 5 minutes
        return requests.size() > 15;
    }

    /**
     * Check if user has unusual activity pattern
     */
    @Profiler
    public boolean checkUnusualUserActivity(String userId, Action action) {
        String key = buildUserActivityKey(userId, action);
        RSet<String> activities = redissonClient.getSet(key);

        String activityMarker = getCurrentTime().format(HOUR_FORMATTER);
        activities.add(activityMarker);
        activities.expire(Duration.ofHours(24)); // Keep for 24 hours

        // Unusual if active in more than 12 different hours in a day
        return activities.size() > 12;
    }

    private String buildVelocityKey(String ipAddress, Action action) {
        String timeWindow = getCurrentTime().format(HOUR_FORMATTER);
        return String.format("shield_svc_velocity:%s:%s:%s", action.name(), ipAddress, timeWindow);
    }

    private String buildMinuteKey(String ipAddress, Action action) {
        String minute = getCurrentTime().format(MINUTE_FORMATTER);
        return String.format("shield_svc_minute:%s:%s:%s", action.name(), ipAddress, minute);
    }

    private String buildNewIndicatorKey(String sourceIdentifier, Action action) {
        String hour = getCurrentTime().format(HOUR_FORMATTER);
        return String.format("shield_svc_indicators:%s:%s:%s", action.name(), sourceIdentifier, hour);
    }

    private String buildAutomatedPatternKey(String ipAddress, Action action) {
        return String.format("shield_svc_automated:%s:%s", action.name(), ipAddress);
    }

    private String buildUserActivityKey(String userId, Action action) {
        String date = getCurrentTime().format(DATE_FORMATTER);
        return String.format("shield_svc_user_activity:%s:%s:%s", action.name(), userId, date);
    }
}
